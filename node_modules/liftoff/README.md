<p align="center">
  <a href="http://liftoffjs.com">
    <img height="100" width="297" src="https://cdn.rawgit.com/tkellen/js-liftoff/master/artwork/liftoff.svg"/>
  </a>
</p>

<p align="center">
  <a href="http://gulpjs.com">
    <img height="257" width="114" src="https://raw.githubusercontent.com/gulpjs/artwork/master/gulp-2x.png">
  </a>
</p>

# liftoff

[![NPM version][npm-image]][npm-url] [![Downloads][downloads-image]][npm-url] [![Build Status][ci-image]][ci-url] [![Coveralls Status][coveralls-image]][coveralls-url]

Launch your command line tool with ease.

## What is it?

[See this blog post][liftoff-blog], [check out this proof of concept][hacker], or read on.

Say you're writing a CLI tool. Let's call it [hacker]. You want to configure it using a `Hackerfile`. This is node, so you install `hacker` locally for each project you use it in. But, in order to get the `hacker` command in your PATH, you also install it globally.

Now, when you run `hacker`, you want to configure what it does using the `Hackerfile` in your current directory, and you want it to execute using the local installation of your tool. Also, it'd be nice if the `hacker` command was smart enough to traverse up your folders until it finds a `Hackerfile`&mdash;for those times when you're not in the root directory of your project. Heck, you might even want to launch `hacker` from a folder outside of your project by manually specifying a working directory. Liftoff manages this for you.

So, everything is working great. Now you can find your local `hacker` and `Hackerfile` with ease. Unfortunately, it turns out you've authored your `Hackerfile` in coffee-script, or some other JS variant. In order to support _that_, you have to load the compiler for it, and then register the extension for it with node. Good news, Liftoff can do that, and a whole lot more, too.

## Usage

```js
const Liftoff = require('liftoff');

const Hacker = new Liftoff({
  name: 'hacker',
  processTitle: 'hacker',
  moduleName: 'hacker',
  configName: 'hackerfile',
  extensions: {
    '.js': null,
    '.json': null,
    '.coffee': 'coffee-script/register',
  },
  v8flags: ['--harmony'], // or v8flags: require('v8flags')
});

Hacker.prepare({}, function (env) {
  Hacker.execute(env, function (env) {
    // Do post-execute things
  });
});
```

## API

### constructor(opts)

Create an instance of Liftoff to invoke your application.

#### opts.name

Sugar for setting `processTitle`, `moduleName`, `configName` automatically.

Type: `String`

Default: `null`

These are equivalent:

```js
const Hacker = Liftoff({
  processTitle: 'hacker',
  moduleName: 'hacker',
  configName: 'hackerfile',
});
```

```js
const Hacker = Liftoff({ name: 'hacker' });
```

Type: `String`

Default: `null`

#### opts.configName

Sets the name of the configuration file Liftoff will attempt to find. Case-insensitive.

Type: `String`

Default: `null`

#### opts.extensions

Set extensions to include when searching for a configuration file. If an external module is needed to load a given extension (e.g. `.coffee`), the module name should be specified as the value for the key.

Type: `Object`

Default: `{".js":null,".json":null}`

**Examples:**

In this example Liftoff will look for `myappfile{.js,.json,.coffee}`. If a config with the extension `.coffee` is found, Liftoff will try to require `coffee-script/require` from the current working directory.

```js
const MyApp = new Liftoff({
  name: 'myapp',
  extensions: {
    '.js': null,
    '.json': null,
    '.coffee': 'coffee-script/register',
  },
});
```

In this example, Liftoff will look for `.myapp{rc}`.

```js
const MyApp = new Liftoff({
  name: 'myapp',
  configName: '.myapp',
  extensions: {
    rc: null,
  },
});
```

In this example, Liftoff will automatically attempt to load the correct module for any javascript variant supported by [interpret] (as long as it does not require a register method).

```js
const MyApp = new Liftoff({
  name: 'myapp',
  extensions: require('interpret').jsVariants,
});
```

#### opts.v8flags

Any flag specified here will be applied to node, not your program. Useful for supporting invocations like `myapp --harmony command`, where `--harmony` should be passed to node, not your program. This functionality is implemented using [flagged-respawn]. To support all v8flags, see [v8flags].

Type: `Array` or `Function`

Default: `null`

If this method is a function, it should take a node-style callback that yields an array of flags.

#### opts.processTitle

Sets what the [process title][process-title] will be.

Type: `String`

Default: `null`

#### opts.completions(type)

A method to handle bash/zsh/whatever completions.

Type: `Function`

Default: `null`

#### opts.configFiles

An array of configuration files to find with each value being a [path arguments](#path-arguments).

The order of the array indicates the priority that config file overrides are applied. See [Config Files](#config-files) for the config file specification and description of overrides.

**Note:** This option is useful if, for example, you want to support an `.apprc` file in addition to an `appfile.js`. If you only need a single configuration file, you probably don't need this. In addition to letting you find multiple files, this option allows more fine-grained control over how configuration files are located.

Type: `Array`

Default: `null`

#### Path arguments

The [`fined`][fined] module accepts a string representing the path to search or an object with the following keys:

- `path` **(required)**

  The path to search. Using only a string expands to this property.

  Type: `String`

  Default: `null`

- `name`

  The basename of the file to find. Extensions are appended during lookup.

  Type: `String`

  Default: Top-level key in `configFiles`

- `extensions`

  The extensions to append to `name` during lookup. See also: [`opts.extensions`](#optsextensions).

  Type: `String` or `Array` or `Object`
  Default: The value of [`opts.extensions`](#optsextensions)

- `cwd`

  The base directory of `path` (if relative).

  Type: `String`

  Default: The value of [`opts.cwd`](#optscwd)

- `findUp`

  Whether the `path` should be traversed up to find the file.

  Type: `Boolean`

  Default: `false`

**Examples:**

In this example Liftoff will look for the `.hacker.js` file relative to the `cwd` as declared in `configFiles`.

```js
const MyApp = new Liftoff({
  name: 'hacker',
  configFiles: [
    { name: '.hacker', path: '.' }
  ],
});
```

In this example, Liftoff will look for `.hackerrc` in the home directory.

```js
const MyApp = new Liftoff({
  name: 'hacker',
  configFiles: [
    {
      name: '.hacker',
      path: '~',
      extensions: {
        rc: null,
      },
    },
  ],
});
```

In this example, Liftoff will look in the `cwd` and then lookup the tree for the `.hacker.js` file.

```js
const MyApp = new Liftoff({
  name: 'hacker',
  configFiles: [
    {
      name: '.hacker',
      path: '.',
      findUp: true,
    },
  ],
});
```

In this example, Liftoff will use the home directory as the `cwd` and looks for `~/.hacker.js`.

```js
const MyApp = new Liftoff({
  name: 'hacker',
  configFiles: [
    {
      name: '.hacker',
      path: '.',
      cwd: '~',
    },
  ],
});
```

### prepare(opts, callback(env))

Prepares the environment for your application with provided options, and invokes your callback with the calculated environment as the first argument. The environment can be modified before using it as the first argument to `execute`.

**Example Configuration w/ Options Parsing:**

```js
const Liftoff = require('liftoff');
const MyApp = new Liftoff({ name: 'myapp' });
const argv = require('minimist')(process.argv.slice(2));
const onExecute = function (env, argv) {
  // Do post-execute things
};
const onPrepare = function (env) {
  console.log('my environment is:', env);
  console.log('my liftoff config is:', this);
  MyApp.execute(env, onExecute);
};
MyApp.prepare(
  {
    cwd: argv.cwd,
    configPath: argv.myappfile,
    preload: argv.preload,
    completion: argv.completion,
  },
  onPrepare
);
```

**Example w/ modified environment**

```js
const Liftoff = require('liftoff');
const Hacker = new Liftoff({
  name: 'hacker',
  configFiles: [
    { name: '.hacker', path: '.', cwd: '~' }
  ],
});
const onExecute = function (env, argv) {
  // Do post-execute things
};
const onPrepare = function (env) {
  const config = env.config['.hacker'];
  Hacker.execute(env, config.forcedFlags, onExecute);
};
Hacker.prepare({}, onPrepare);
```

#### opts.cwd

Change the current working directory for this execution. Relative paths are calculated against `process.cwd()`.

Type: `String`

Default: `process.cwd()`

**Example Configuration:**

```js
const argv = require('minimist')(process.argv.slice(2));
MyApp.prepare(
  {
    cwd: argv.cwd,
  },
  function (env) {
    MyApp.execute(env, invoke);
  }
);
```

**Matching CLI Invocation:**

```
myapp --cwd ../
```

#### opts.configPath

Don't search for a config, use the one provided. **Note:** Liftoff will assume the current working directory is the directory containing the config file unless an alternate location is explicitly specified using `cwd`.

Type: `String`

Default: `null`

**Example Configuration:**

```js
var argv = require('minimist')(process.argv.slice(2));
MyApp.prepare(
  {
    configPath: argv.myappfile,
  },
  function (env) {
    MyApp.execute(env, invoke);
  }
);
```

**Matching CLI Invocation:**

```sh
myapp --myappfile /var/www/project/Myappfile.js
```

**Examples using `cwd` and `configPath` together:**

These are functionally identical:

```sh
myapp --myappfile /var/www/project/Myappfile.js
myapp --cwd /var/www/project
```

These can run myapp from a shared directory as though it were located in another project:

```sh
myapp --myappfile /Users/<USER>/Myappfile.js --cwd /var/www/project1
myapp --myappfile /Users/<USER>/Myappfile.js --cwd /var/www/project2
```

#### opts.preload

A string or array of modules to attempt requiring from the local working directory before invoking the execute callback.

Type: `String|Array`
Default: `null`

**Example Configuration:**

```js
var argv = require('minimist')(process.argv.slice(2));
MyApp.prepare(
  {
    preload: argv.preload,
  },
  function (env) {
    MyApp.execute(env, invoke);
  }
);
```

**Matching CLI Invocation:**

```sh
myapp --preload coffee-script/register
```

#### callback(env)

A function called after your environment is prepared. A good place to modify the environment before calling `execute`. When invoked, `this` will be your instance of Liftoff. The `env` param will contain the following keys:

- `cwd`: the current working directory
- `preload`: an array of modules that liftoff tried to pre-load
- `configNameSearch`: the config files searched for
- `configPath`: the full path to your configuration file (if found)
- `configBase`: the base directory of your configuration file (if found)
- `modulePath`: the full path to the local module your project relies on (if found)
- `modulePackage`: the contents of the local module's package.json (if found)
- `configFiles`: an array of filepaths for each found config file (filepath values will be null if not found)
- `config`: an array of loaded config objects in the same order as `configFiles`

### execute(env, [forcedFlags], callback(env, argv))

A function to start your application, based on the `env` given. Optionally takes an array of `forcedFlags`, which will force a respawn with those node or V8 flags during startup. Invokes your callback with the environment and command-line arguments (minus node & v8 flags) after the application has been executed.

**Example:**

```js
const Liftoff = require('liftoff');
const MyApp = new Liftoff({ name: 'myapp' });
const onExecute = function (env, argv) {
  // Do post-execute things
  console.log('my environment is:', env);
  console.log('my cli options are:', argv);
  console.log('my liftoff config is:', this);
};
const onPrepare = function (env) {
  var forcedFlags = ['--trace-deprecation'];
  MyApp.execute(env, forcedFlags, onExecute);
};
MyApp.prepare({}, onPrepare);
```

#### callback(env, argv)

A function called after your application is executed. When invoked, `this` will be your instance of Liftoff, `argv` will be all command-line arguments (minus node & v8 flags), and `env` will contain the following keys:

- `cwd`: the current working directory
- `preload`: an array of modules that liftoff tried to pre-load
- `configNameSearch`: the config files searched for
- `configPath`: the full path to your configuration file (if found)
- `configBase`: the base directory of your configuration file (if found)
- `modulePath`: the full path to the local module your project relies on (if found)
- `modulePackage`: the contents of the local module's package.json (if found)
- `configFiles`: an array of filepaths for each found config file (filepath values will be null if not found)
- `config`: an array of loaded config objects in the same order as `configFiles`

### events

#### `on('preload:before', function(name) {})`

Emitted before a module is pre-load. (But for only a module which is specified by `opts.preload`.)

```js
var Hacker = new Liftoff({ name: 'hacker', preload: 'coffee-script' });
Hacker.on('preload:before', function (name) {
  console.log('Requiring external module: ' + name + '...');
});
```

#### `on('preload:success', function(name, module) {})`

Emitted when a module has been pre-loaded.

```js
var Hacker = new Liftoff({ name: 'hacker' });
Hacker.on('preload:success', function (name, module) {
  console.log('Required external module: ' + name + '...');
  // automatically register coffee-script extensions
  if (name === 'coffee-script') {
    module.register();
  }
});
```

#### `on('preload:failure', function(name, err) {})`

Emitted when a requested module cannot be preloaded.

```js
var Hacker = new Liftoff({ name: 'hacker' });
Hacker.on('preload:failure', function (name, err) {
  console.log('Unable to load:', name, err);
});
```

#### `on('loader:success, function(name, module) {})`

Emitted when a loader that matches an extension has been loaded.

```js
var Hacker = new Liftoff({
  name: 'hacker',
  extensions: {
    '.ts': 'ts-node/register',
  },
});
Hacker.on('loader:success', function (name, module) {
  console.log('Required external module: ' + name + '...');
});
```

#### `on('loader:failure', function(name, err) {})`

Emitted when no loader for an extension can be loaded. Emits an error for each failed loader.

```js
var Hacker = new Liftoff({
  name: 'hacker',
  extensions: {
    '.ts': 'ts-node/register',
  },
});
Hacker.on('loader:failure', function (name, err) {
  console.log('Unable to load:', name, err);
});
```

#### `on('respawn', function(flags, child) {})`

Emitted when Liftoff re-spawns your process (when a [`v8flags`](#optsv8flags) is detected).

```js
var Hacker = new Liftoff({
  name: 'hacker',
  v8flags: ['--harmony'],
});
Hacker.on('respawn', function (flags, child) {
  console.log('Detected node flags:', flags);
  console.log('Respawned to PID:', child.pid);
});
```

Event will be triggered for this command:
`hacker --harmony commmand`

## Config files

Liftoff supports a small definition of config files, but all details provided by users will be available in `env.config`.

### `extends`

All `extends` properties will be traversed and become the basis for the resulting config object. Any path provided for `extends` will be loaded with node's `require`, so all extensions and loaders supported on the Liftoff instance will be available to them.

### Field matching the `configName`

Users can override the `configPath` via their config files by specifying a field with the same name as the primary `configName`. For example, the `hackerfile` property in a `configFile` will resolve the `configPath` and `configBase` against the path.

### `preload`

If specified as a string or array of strings, they will be added to the list of preloads in the environment.

## Examples

Check out how [gulp][gulp-cli-index] uses Liftoff.

For a bare-bones example, try [the hacker project][hacker-index].

To try the example, do the following:

1. Install the sample project `hacker` with `npm install -g hacker`.
2. Make a `Hackerfile.js` with some arbitrary javascript it.
3. Install hacker next to it with `npm install hacker`.
4. Run `hacker` while in the same parent folder.

## License

MIT

<!-- prettier-ignore-start -->
[downloads-image]: https://img.shields.io/npm/dm/liftoff.svg?style=flat-square
[npm-url]: https://www.npmjs.com/package/liftoff
[npm-image]: https://img.shields.io/npm/v/liftoff.svg?style=flat-square

[ci-url]: https://github.com/gulpjs/liftoff/actions?query=workflow:dev
[ci-image]: https://img.shields.io/github/workflow/status/gulpjs/liftoff/dev?style=flat-square

[coveralls-url]: https://coveralls.io/r/gulpjs/liftoff
[coveralls-image]: https://img.shields.io/coveralls/gulpjs/liftoff/master.svg?style=flat-square
<!-- prettier-ignore-end -->

<!-- prettier-ignore-start -->
[liftoff-blog]: https://bocoup.com/blog/building-command-line-tools-in-node-with-liftoff

[hacker]: https://github.com/gulpjs/hacker
[interpret]: https://github.com/gulpjs/interpret
[flagged-respawn]: http://github.com/gulpjs/flagged-respawn
[v8flags]: https://github.com/gulpjs/v8flags
[fined]: https://github.com/gulpjs/fined

[process-title]: http://nodejs.org/api/process.html#process_process_title

[gulp-cli-index]: https://github.com/gulpjs/gulp-cli/blob/master/index.js
[hacker-index]: https://github.com/gulpjs/js-hacker/blob/master/bin/hacker.js
<!-- prettier-ignore-end -->
