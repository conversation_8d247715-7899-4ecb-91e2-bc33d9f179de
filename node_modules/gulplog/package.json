{"name": "gulplog", "version": "2.2.0", "description": "Logger for gulp and gulp plugins", "author": "Gulp Team <<EMAIL>> (http://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>"], "repository": "gulpjs/gulplog", "license": "MIT", "engines": {"node": ">= 10.13.0"}, "main": "index.js", "types": "index.d.ts", "files": ["LICENSE", "index.js", "index.d.ts"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"glogg": "^2.2.0"}, "devDependencies": {"eslint": "^7.32.0", "eslint-config-gulp": "^5.0.1", "eslint-plugin-node": "^11.1.0", "expect": "^27.3.1", "mocha": "^8.4.0", "nyc": "^15.1.0"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["gulp", "log", "logging"]}