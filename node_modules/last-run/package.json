{"name": "last-run", "version": "2.0.0", "description": "Capture and retrieve the last time a function was run", "author": "Gulp Team <<EMAIL>> (http://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>"], "repository": "gulpjs/last-run", "license": "MIT", "engines": {"node": ">= 10.13.0"}, "main": "index.js", "files": ["LICENSE", "index.js"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "devDependencies": {"eslint": "^7.32.0", "eslint-config-gulp": "^5.0.1", "eslint-plugin-node": "^11.1.0", "expect": "^27.3.1", "mocha": "^8.4.0", "nyc": "^15.1.0"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["execution", "function", "last run", "timing"]}