{"name": "gulp-sass", "version": "6.0.1", "description": "Gulp plugin for sass", "main": "index.js", "engines": {"node": ">=12"}, "scripts": {"lint": "eslint --report-unused-disable-directives --ignore-path .gitignore .", "fix": "npm run lint -- --fix", "mocha": "mocha", "test": "npm run test:node-sass && npm run test:dart-sass && npm run test:legacy-dart-sass && npm run test:sass-embedded", "test:node-sass": "mocha", "test:dart-sass": "mocha -- --sass", "test:legacy-dart-sass": "mocha -- --sass --legacy", "test:sass-embedded": "mocha -- --embedded"}, "repository": {"type": "git", "url": "git+https://github.com/dlmanning/gulp-sass.git"}, "keywords": ["gulpplugin", "sass", "gulp"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/dlmanning/gulp-sass/issues"}, "homepage": "https://github.com/dlmanning/gulp-sass#readme", "files": ["index.js", "legacy.js"], "dependencies": {"lodash.clonedeep": "^4.5.0", "picocolors": "^1.0.0", "plugin-error": "^1.0.1", "replace-ext": "^2.0.0", "strip-ansi": "^6.0.1", "vinyl-sourcemaps-apply": "^0.2.1"}, "devDependencies": {"autoprefixer": "^10.4.0", "eslint": "^8.5.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.25.3", "globule": "^1.3.3", "gulp": "^4.0.2", "gulp-postcss": "^9.0.1", "gulp-sourcemaps": "^3.0.0", "gulp-tap": "^2.0.0", "mocha": "^9.1.3", "node-sass": "^7.0.1", "postcss": "^8.4.5", "rimraf": "^5.0.10", "sass": "^1.45.1", "sass-embedded": "^1.49.9", "vinyl": "^2.2.1"}}