{"name": "undertaker", "version": "2.0.0", "description": "Task registry that allows composition through series/parallel methods.", "author": "Gulp Team <<EMAIL>> (https://gulpjs.com/)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "repository": "gulpjs/undertaker", "license": "MIT", "engines": {"node": ">=10.13.0"}, "main": "index.js", "files": ["LICENSE", "index.js", "lib"], "scripts": {"lint": "eslint .", "pretest": "npm run lint", "test": "nyc mocha --async-only"}, "dependencies": {"bach": "^2.0.1", "fast-levenshtein": "^3.0.0", "last-run": "^2.0.0", "undertaker-registry": "^2.0.0"}, "devDependencies": {"async-once": "^2.0.0", "del": "^6.1.1", "eslint": "^7.32.0", "eslint-config-gulp": "^5.0.1", "eslint-plugin-node": "^11.1.0", "expect": "^27.5.1", "mocha": "^8.4.0", "nyc": "^15.1.0", "once": "^1.4.0", "through2": "^4.0.2", "undertaker-common-tasks": "^2.0.0", "undertaker-task-metadata": "^2.0.0", "vinyl-fs": "^4.0.0"}, "nyc": {"reporter": ["lcov", "text-summary"]}, "prettier": {"singleQuote": true}, "keywords": ["registry", "runner", "task"]}