const gulp = require('gulp');
const sass = require('gulp-sass')(require('sass'));
const sourcemaps = require('gulp-sourcemaps');
const postcss = require('gulp-postcss');
const autoprefixer = require('autoprefixer');
const cleanCSS = require('gulp-clean-css');
const browserSync = require('browser-sync').create();

const paths = {
  scss: 'assets/scss/admin.scss',
  scssWatch: 'assets/scss/**/*.scss',
  cssDest: 'assets/css',
};

function stylesDev() {
  return gulp.src(paths.scss)
    .pipe(sourcemaps.init())
    .pipe(sass().on('error', sass.logError))
    .pipe(postcss([autoprefixer()]))
    .pipe(sourcemaps.write('.'))
    .pipe(gulp.dest(paths.cssDest))
    .pipe(browserSync.stream({ match: '**/*.css' }));
}

function stylesBuild() {
  return gulp.src(paths.scss)
    .pipe(sass().on('error', sass.logError))
    .pipe(postcss([autoprefixer()]))
    .pipe(cleanCSS({ level: 2 }))
    .pipe(gulp.dest(paths.cssDest));
}

function serve() {
  browserSync.init({
    proxy: 'https://wordpress.test',
    open: false,
    https: true,
    notify: false,
    files: [
      'awesome-reports.php',
      'assets/js/**/*.js',
      'assets/css/**/*.css',
    ],
    snippetOptions: {
      rule: {
        match: /<\/body>/i,
        fn: function (snippet, match) {
          return snippet + match;
        }
      }
    }
  });

  gulp.watch(paths.scssWatch, stylesDev);
}

const dev = gulp.series(stylesDev, serve);
const build = gulp.series(stylesBuild);

exports.dev = dev;
exports.build = build;
exports.default = dev;

