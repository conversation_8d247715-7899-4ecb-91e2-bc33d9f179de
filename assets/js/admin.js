(function(){
  // Build last 30 days labels deterministically
  const days = 30;
  const labels = [];
  const dateObjs = [];
  const now = new Date();
  for (let i = days - 1; i >= 0; i--) {
    const d = new Date(now);
    d.setHours(12,0,0,0);
    d.setDate(now.getDate() - i);
    labels.push(d.toLocaleDateString(undefined, { month: 'short', day: 'numeric' }));
    dateObjs.push(d);
  }

  // Response time chart (fixed sample data + gradient + improved tooltip and hover)
  const canvas = document.getElementById('ar-response-time-chart');
  if (canvas && window.Chart) {
    const ctx = canvas.getContext('2d');

    const sampleResponseMs = [
      320, 310, 305, 295, 290, 300, 315, 310, 305, 520,
      340, 330, 325, 315, 310, 300, 295, 290, 285, 280,
      275, 270, 265, 600, 320, 315, 310, 305, 300, 295
    ];

    const gradient = (() => {
      const g = ctx.createLinearGradient(0, 0, 0, canvas.height || 220);
      g.addColorStop(0, 'rgba(34,113,177,0.25)');
      g.addColorStop(1, 'rgba(34,113,177,0.00)');
      return g;
    })();

    const hoverLine = {
      id: 'hoverLine',
      afterDatasetsDraw(chart) {
        const active = chart.tooltip?.getActiveElements?.();
        if (!active || active.length === 0) return;
        const { ctx, chartArea: { top, bottom }, scales: { x } } = chart;
        const idx = active[0].index;
        const xPos = x.getPixelForValue(idx);
        ctx.save();
        ctx.beginPath();
        ctx.moveTo(xPos, top);
        ctx.lineTo(xPos, bottom);
        ctx.lineWidth = 1;
        ctx.strokeStyle = 'rgba(0,0,0,0.12)';
        ctx.setLineDash([4, 4]);
        ctx.stroke();
        ctx.setLineDash([]);
        ctx.restore();
      }
    };

    // eslint-disable-next-line no-undef
    new Chart(ctx, {
      type: 'line',
      data: {
        labels,
        datasets: [{
          label: 'Response time (ms)',
          data: sampleResponseMs,
          borderColor: 'rgba(34, 113, 177, 1)',
          backgroundColor: gradient,
          fill: true,
          tension: 0.25,
          pointRadius: 0,
          pointHoverRadius: 6,
          pointHoverBorderWidth: 3,
          pointHoverBackgroundColor: 'rgba(34,113,177,1)',
          pointHoverBorderColor: '#fff',
          borderWidth: 2,
          hoverBorderWidth: 3,
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: { mode: 'index', intersect: false },
        layout: {
          padding: {
            top: 20,
            right: 20,
            bottom: 15,
            left: 15
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: { callback: (v) => v + 'ms' },
            grid: { color: 'rgba(0,0,0,0.05)' }
          },
          x: {
            grid: { display: false },
            ticks: {
              callback: function(_, index) {
                // Show first label, then every 7th label, and last label
                if (index === 0 || index % 7 === 0 || index === labels.length - 1) {
                  return labels[index];
                }
                return '';
              },
              maxRotation: 0,
              minRotation: 0
            }
          }
        },
        plugins: {
          legend: { display: false },
          tooltip: {
            enabled: true,
            backgroundColor: '#ffffff',
            titleColor: '#111827',
            bodyColor: '#111827',
            borderColor: '#e5e7eb',
            borderWidth: 1,
            padding: 10,
            displayColors: false,
            callbacks: {
              title: (items) => items?.[0]?.label ?? '',
              label: (ctx) => `${ctx.parsed.y}ms`
            },
            titleFont: { size: 12 },
            bodyFont: { size: 14, weight: 'bold' }
          }
        }
      },
      plugins: [hoverLine]
    });
  }

  // Daily pills: deterministic 30-day data with tooltips
  const pills = document.getElementById('ar-uptime-pills');
  if (pills) {
    // Define fixed up/down days (0=up,1=down)
    const downIdx = new Set([9, 23]); // match the spikes roughly
    for (let i = 0; i < days; i++) {
      const statusDown = downIdx.has(i);
      const pill = document.createElement('div');
      pill.className = 'ar-pill ' + (statusDown ? 'down' : 'up');
      const label = labels[i];
      const tip = statusDown ? `${label}<br><strong style="font-size: 14px;">Down 5m 3s</strong>` : `${label}<br><strong style="font-size: 14px;">Up 100%</strong>`;
      pill.dataset.tooltip = tip;
      pill.setAttribute('aria-label', tip);
      pills.appendChild(pill);
    }

    // Initialize Tooltipster if available
    if (window.jQuery && jQuery.fn.tooltipster) {
      jQuery(pills).find('.ar-pill').tooltipster({
        theme: 'tooltipster-light',
        delay: 100,
        animation: 'fade',
        contentAsHTML: true,
        functionInit: function(instance, helper){
          const content = helper.origin.getAttribute('data-tooltip');
          instance.content(content);
        }
      });
    }
  }

})();

