{"version": 3, "sources": ["admin.scss", "admin.css"], "names": [], "mappings": "AAGA,mBAAA;AACA;EACI,gBAAA;EACA,eAAA;EACA,mBAAA;EACA,6BAAA;ACFJ;;ADKA;EACI,aAAA;EACA,mBAAA;EACA,SAAA;EACA,eAAA;EACA,qBAAA;ACFJ;;ADKA;;;;EAII,qBAAA;EACA,aAAA;EACA,gBAAA;ACFJ;;ADKA;EACI,WAAA;EACA,YAAA;EACA,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,cAAA;ACFJ;;ADKA;EACI,WAAA;EACA,YAAA;ACFJ;;ADKA;EACI,eAAA;EACA,gBAAA;ACFJ;;ADKA;EACI,gBAAA;EACA,6BAAA;EACA,eAAA;EACA,sBAAA;ACFJ;;ADKA;EACI,aAAA;EACA,mBAAA;EACA,SAAA;EACA,eAAA;ACFJ;;ADKA;EACI,gBAAA;EACA,oCAAA;EACA,kBAAA;EACA,iBAAA;EACA,eAAA;EACA,cAAA;EACA,gBAAA;EACA,YAAA;EACA,yCAAA;EACA,wEAAA;ACFJ;;ADKA;EACI,qBAAA;EACA,6BAAA;EACA,aAAA;ACFJ;;ADKA;EACI,qBAAA;ACFJ;;ADKA;EACI,sBAAA;EACA,0BAAA;ACFJ;;ADKA;;EAEI,6BAAA;ACFJ;;ADKA;EACI,mBAAA;EACA,eAAA;ACFJ;;ADKA;EACI,0BAAA;ACFJ;;ADKA;EACI,aAAA;EACA,qCAAA;EACA,SAAA;EACA,mBAAA;ACFJ;;ADKA;EACI,gBAAA;EACA,sBAAA;EACA,2BAAA;EACA,kBAAA;EACA,6CAAA;ACFJ;;ADKA;EACI,kBAAA;EACA,eAAA;EACA,gBAAA;EACA,cAAA;EACA,aAAA;EACA,mBAAA;EACA,SAAA;EACA,kBAAA;EACA,8BAAA;EACA,gCAAA;EACA,2BAAA;EACA,4BAAA;ACFJ;;ADKA;EACI,WAAA;EACA,YAAA;EACA,eAAA;EACA,cAAA;ACFJ;;ADKA;EACI,eAAA;EACA,iBAAA;EACA,cAAA;EACA,oBAAA;EACA,cAAA;EACA,aAAA;EACA,qBAAA;EACA,SAAA;ACFJ;;ADMA;EACI,eAAA;EACA,cAAA;EACA,0BAAA;EACA,YAAA;ACHJ;;ADMA;;EAEI,cAAA;EACA,kBAAA;EACA,kBAAA;ACHJ;;ADMA,4CAAA;AACA;EACI,gBAAA;EACA,kBAAA;EACA,gCAAA;EACA,aAAA;EACA,8BAAA;EACA,mBAAA;EACA,SAAA;EACA,gBAAA;EACA,eAAA;EACA,cAAA;EACA,gBAAA;ACHJ;;ADKA;EACI,mBAAA;ACFJ;;ADIA;EACI,6BAAA;ACDJ;;ADIA;EACI,gBAAA;EACA,cAAA;EACA,cAAA;EACA,eAAA;EACA,eAAA;ACDJ;;ADIA;EACI,OAAA;EACA,mBAAA;EACA,gBAAA;EACA,uBAAA;EACA,gBAAA;EACA,cAAA;EACA,eAAA;ACDJ;;ADIA;EACI,cAAA;EACA,cAAA;EACA,gBAAA;EACA,eAAA;ACDJ;;ADKA,wBAAA;AACA;EACI,aAAA;EACA,qCAAA;EACA,SAAA;EACA,mBAAA;ACFJ;;ADIA;EACI,aAAA;EACA,sBAAA;EACA,SAAA;EACA,YAAA;ACDJ;;ADGA;EACI,OAAA;ACAJ;;ADEA;EAAsB,mBAAA;ACEtB;;ADDA;EAAuB,mBAAA;ACKvB;;ADHA;EAAuC,kBAAA;ACOvC;;ADNA;EAAuB,WAAA;EAAa,mBAAA;EAAqB,mBAAA;ACYzD;;ADXA;EAA0C,aAAA;EAAe,sBAAA;EAAwB,SAAA;ACiBjF;;ADfA;EACI,cAAA;EACA,eAAA;ACkBJ;;ADfA;EACI,aAAA;EACA,mBAAA;EACA,SAAA;EACA,kBAAA;ACkBJ;;ADhBA;EACI,WAAA;EACA,YAAA;EACA,kBAAA;EACA,mBAAA;EACA,4CAAA;EACA,4BAAA;ACmBJ;;ADjBA;EACI;IAAK,4CAAA;ECqBP;EDpBE;IAAM,4CAAA;ECuBR;EDtBE;IAAO,4CAAA;ECyBT;AACF;ADxBA;EACI,eAAA;EACA,gBAAA;AC0BJ;;ADvBA;EACI,qBAAA;EACA,iBAAA;EACA,mBAAA;EACA,eAAA;EACA,gBAAA;AC0BJ;;ADxBA;EACI,mBAAA;EACA,cAAA;EACA,yBAAA;AC2BJ;;ADxBA;EACI,eAAA;EACA,gBAAA;EACA,cAAA;EACA,cAAA;EACA,mBAAA;AC2BJ;;ADxBA;EACI,mBAAA;EACA,gBAAA;AC2BJ;;ADzBA;EACI,aAAA;EACA,gBAAA;EACA,kBAAA;AC4BJ;;AD1BA;EACI,sBAAA;EACA,uBAAA;EACA,eAAA;AC6BJ;;AD1BA,UAAA;AACA;EACI,uBAAA;EACA,YAAA;EACA,UAAA;EACA,mBAAA;AC6BJ;;AD3BA;EACI,aAAA;EACA,mBAAA;EACA,8BAAA;EACA,mBAAA;AC8BJ;;AD5BA;EAAkB,gBAAA;EAAkB,eAAA;EAAiB,cAAA;ACkCrD;;ADjCA;EAAoB,gBAAA;EAAkB,eAAA;EAAiB,cAAA;ACuCvD;;ADrCA;EACI,aAAA;EACA,sCAAA;EACA,QAAA;EACA,UAAA;EACA,kBAAA;ACwCJ;;ADtCA;EACI,WAAA;EACA,YAAA;EACA,oBAAA;EACA,mBAAA;EACA,YAAA;EACA,8BAAA;ACyCJ;;ADvCA;EAAY,WAAA;EAAa,yBAAA;AC4CzB;;AD3CA;EAAqB,gBAAA;EAAkB,gBAAA;EAAkB,eAAA;EAAiB,cAAA;EAAgB,iBAAA;EAAmB,gCAAA;ACoD7G;;ADnDA;EAAqB,kBAAA;EAAoB,gCAAA;EAAkC,eAAA;EAAiB,cAAA;AC0D5F;;ADzDA;EAAuC,mBAAA;AC6DvC;;AD5DA;EAAkB,qBAAA;EAAuB,gBAAA;EAAkB,mBAAA;EAAqB,eAAA;ACmEhF;;ADlEA;EAA2B,mBAAA;EAAqB,cAAA;EAAgB,yBAAA;ACwEhE;;ADtEA;EACI,YAAA;ACyEJ;;ADvEA;EAAc,mBAAA;AC2Ed;;AD1EA;EAAgB,mBAAA;AC8EhB;;AD5EA;EACI,cAAA;AC+EJ;;AD5EA,4BAAA;AACA;EACI,8BAAA;EACA,yBAAA;EACA,oCAAA;EACA,qDAAA;EACA,6BAAA;AC+EJ;;AD7EA;EACI,4BAAA;EACA,0BAAA;EACA,2BAAA;EACA,yBAAA;ACgFJ;;AD9EA;EACI,oCAAA;ACiFJ;;AD/EA;EACI,oCAAA;ACkFJ;;AD/EA;EACI;IAA+B,qCAAA;ECmFjC;EDlFE;IAAsB,mBAAA;ECqFxB;AACF;ADnFA;EACI;IAA+B,0BAAA;ECsFjC;EDrFE;IAAsB,mBAAA;ECwFxB;AACF;ADtFA;EACI;IACI,qCAAA;ECwFN;AACF;ADrFA;EACI;IACI,iBAAA;IACA,2BAAA;ECuFN;EDpFE;IACI,kBAAA;IACA,0BAAA;ECsFN;EDnFE;IACI,eAAA;ECqFN;EDlFE;IACI,WAAA;IACA,YAAA;ECoFN;EDjFE;IACI,WAAA;IACA,YAAA;ECmFN;EDhFE;IACI,gBAAA;IACA,eAAA;IACA,iBAAA;IACA,YAAA;ECkFN;ED/EE;IACI,0BAAA;ECiFN;ED9EE;IACI,sBAAA;IACA,uBAAA;IACA,QAAA;ECgFN;ED7EE;IACI,sBAAA;IACA,uBAAA;IACA,QAAA;IACA,kBAAA;EC+EN;ED5EE;IACI,eAAA;IACA,QAAA;EC8EN;ED3EE;IACI,QAAA;IACA,eAAA;EC6EN;ED1EE;IACI,QAAA;IACA,oBAAA;EC4EN;AACF", "file": "admin.css", "sourcesContent": ["// Source SCSS for Awesome Reports admin styles\n// This file is compiled to assets/css/admin.css via Gulp\n\n/* Toolbar Styles */\n.awesome-reports-toolbar-primary {\n    background: #fff;\n    padding: 10px 0;\n    margin: 0 0 0 -20px;\n    border-bottom: 1px solid #ddd;\n}\n\n.awesome-reports-logo {\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    padding: 0 20px;\n    text-decoration: none;\n}\n\n.awesome-reports-logo:focus,\n.awesome-reports-logo:hover,\n.awesome-reports-logo:active,\n.awesome-reports-logo:visited {\n    text-decoration: none;\n    outline: none;\n    box-shadow: none;\n}\n\n.awesome-reports-logo-icon {\n    width: 40px;\n    height: 40px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-shrink: 0;\n}\n\n.awesome-reports-logo-icon svg {\n    width: 40px;\n    height: 40px;\n}\n\n.awesome-reports-logo-text {\n    font-size: 18px;\n    font-weight: 600;\n}\n\n.awesome-reports-toolbar-secondary {\n    background: #fff;\n    border-bottom: 1px solid #ddd;\n    padding: 12px 0;\n    margin: 0 0 20px -20px;\n}\n\n.awesome-reports-filters {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n    padding: 0 20px;\n}\n\n.awesome-reports-time-filter {\n    background: #fff;\n    border: 1px solid #c3c4c7!important;\n    border-radius: 4px;\n    padding: 8px 12px;\n    font-size: 14px;\n    color: #1d2327;\n    min-width: 160px;\n    height: 32px;\n    box-shadow: 0 1px 1px rgba(0,0,0,.04);\n    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.awesome-reports-time-filter:focus {\n    border-color: #2271b1;\n    box-shadow: 0 0 0 1px #2271b1;\n    outline: none;\n}\n\n.awesome-reports-time-filter:hover {\n    border-color: #8c8f94;\n}\n\n.awesome-reports-table {\n    width: 100% !important;\n    max-width: none !important;\n}\n\n.awesome-reports-table th,\n.awesome-reports-table td {\n    padding: 15px 10px !important;\n}\n\n.awesome-reports-card {\n    margin-bottom: 20px;\n    max-width: 100%;\n}\n\n.awesome-reports-card .card {\n    max-width: none !important;\n}\n\n.awesome-reports-grid {\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    gap: 12px;\n    margin-bottom: 30px;\n}\n\n.awesome-reports-update-card {\n    background: #fff;\n    border: 1px solid #ddd;\n    padding: 15px 15px 5px 15px;\n    border-radius: 6px;\n    box-shadow: 0px 1px 2px rgba(16, 24, 40, .1);\n}\n\n.awesome-reports-update-card h3 {\n    margin: 0 0 15px 0;\n    font-size: 14px;\n    font-weight: 600;\n    color: #23282d;\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    padding: 10px 15px;\n    margin: -15px -15px 15px -15px;\n    border-bottom: 1px solid #e1e1e1;\n    border-top-left-radius: 8px;\n    border-top-right-radius: 8px;\n}\n\n.awesome-reports-update-card .dashicons {\n    width: 20px;\n    height: 20px;\n    font-size: 20px;\n    color: #98a2b3;\n}\n\n.awesome-reports-update-card .count {\n    font-size: 48px;\n    font-weight: bold;\n    color: #1d2327;\n    margin: 10px 0 5px 0;\n    line-height: 1;\n    display: flex;\n    align-items: baseline;\n    gap: 10px;\n}\n\n\n.awesome-reports-update-card .updates-list {\n    font-size: 13px;\n    color: #646970;\n    margin: 15px -15px 0 -15px;\n    opacity: 0.8;\n}\n\n.update-more,\n.update-none {\n    color: #646970;\n    font-style: italic;\n    padding: 10px 15px;\n}\n\n/* Table-style update items like incidents */\n.awesome-reports-update-card .update-item {\n    margin-bottom: 0;\n    padding: 10px 12px;\n    border-bottom: 1px solid #f3f4f6;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: 12px;\n    background: #fff;\n    font-size: 13px;\n    color: #111827;\n    line-height: 1.4;\n}\n.awesome-reports-update-card .update-item:nth-child(odd) {\n    background: #fafafa;\n}\n.awesome-reports-update-card .update-item:first-child {\n    border-top: 1px solid #e5e7eb;\n}\n\n.awesome-reports-update-card .update-date {\n    font-weight: 500;\n    color: #6b7280;\n    flex-shrink: 0;\n    min-width: 55px;\n    font-size: 13px;\n}\n\n.awesome-reports-update-card .update-name {\n    flex: 1;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    font-weight: 500;\n    color: #111827;\n    font-size: 13px;\n}\n\n.awesome-reports-update-card .update-version {\n    color: #6b7280;\n    flex-shrink: 0;\n    font-weight: 500;\n    font-size: 13px;\n}\n\n\n/* Uptime Grid + Cards */\n.awesome-reports-uptime-grid {\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    gap: 12px;\n    margin-bottom: 30px;\n}\n.ar-uptime-stack {\n    display: flex;\n    flex-direction: column;\n    gap: 12px;\n    height: 100%;\n}\n.ar-uptime-stack .awesome-reports-update-card {\n    flex: 1;\n}\n.ar-uptime-response { grid-column: span 2; }\n.ar-uptime-incidents { grid-column: span 1; }\n\n.ar-uptime-overview .ar-overview-top { margin-bottom: 4px; }\n.ar-overview-divider { height: 1px; background: #eef0f2; margin: 10px 0 12px; }\n.ar-uptime-overview .ar-overview-bottom { display: flex; flex-direction: column; gap: 10px; }\n\n.ar-uptime-card .ar-subtle {\n    color: #646970;\n    margin-top: 8px;\n}\n\n.ar-status-row {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n    margin: 8px 0 12px;\n}\n.ar-status-dot {\n    width: 18px;\n    height: 18px;\n    border-radius: 50%;\n    background: #22c55e;\n    box-shadow: 0 0 0 4px rgba(34,197,94,0.2);\n    animation: pulse 2s infinite;\n}\n@keyframes pulse {\n    0% { box-shadow: 0 0 0 4px rgba(34,197,94,0.2); }\n    50% { box-shadow: 0 0 0 6px rgba(34,197,94,0.3); }\n    100% { box-shadow: 0 0 0 4px rgba(34,197,94,0.2); }\n}\n.ar-status-text {\n    font-size: 24px;\n    font-weight: 800;\n}\n\n.ar-badge {\n    display: inline-block;\n    padding: 4px 10px;\n    border-radius: 12px;\n    font-size: 12px;\n    font-weight: 600;\n}\n.ar-badge-up {\n    background: #e8f5e8;\n    color: #0b7a0b;\n    border: 1px solid #c6e1c6;\n}\n\n.ar-big-number {\n    font-size: 40px;\n    font-weight: 800;\n    line-height: 1;\n    color: #1d2327;\n    margin-bottom: 10px;\n}\n\n.ar-uptime-response {\n    grid-column: span 2;\n    overflow: hidden;\n}\n.ar-chart-wrap {\n    height: 240px;\n    overflow: hidden;\n    position: relative;\n}\n.ar-chart-wrap canvas {\n    width: 100% !important;\n    height: 100% !important;\n    max-width: 100%;\n}\n\n/* Pills */\n.ar-pills-panel {\n    background: transparent;\n    border: none;\n    padding: 0;\n    margin-bottom: 15px;\n}\n.ar-pills-head {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 10px;\n}\n.ar-pills-title { font-weight: 600; font-size: 14px; color: #1e293b; }\n.ar-pills-percent { font-weight: 700; font-size: 14px; color: #1e293b; }\n\n.ar-pills {\n    display: grid;\n    grid-template-columns: repeat(30, 1fr);\n    gap: 4px;\n    padding: 0;\n    margin-bottom: 8px;\n}\n.ar-pill {\n    width: 100%;\n    height: 32px;\n    border-radius: 999px;\n    background: #22c55e;\n    border: none;\n    transition: opacity 120ms ease;\n}\n.ar-table { width: 100%; border-collapse: collapse; }\n.ar-table thead th { text-align: left; font-weight: 600; font-size: 12px; color: #6b7280; padding: 8px 12px; border-bottom: 1px solid #e5e7eb; }\n.ar-table tbody td { padding: 10px 12px; border-bottom: 1px solid #f3f4f6; font-size: 13px; color: #111827; }\n.ar-table tbody tr:nth-child(odd) td { background: #fafafa; }\n.ar-pill-status { display: inline-block; padding: 2px 8px; border-radius: 10px; font-size: 12px; }\n.ar-pill-status.resolved { background: #e8f5e8; color: #0b7a0b; border: 1px solid #c6e1c6; }\n\n.ar-pill:hover {\n    opacity: 0.7;\n}\n.ar-pill.up { background: #22c55e; }\n.ar-pill.down { background: #ef4444; }\n\n.ar-pills-foot {\n    color: #64748b;\n}\n\n/* light tooltipster theme */\n.tooltipster-sidetip.tooltipster-light .tooltipster-box {\n    background: #ffffff !important;\n    color: #1e293b !important;\n    border: 1px solid #e2e8f0 !important;\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;\n    border-radius: 6px !important;\n}\n.tooltipster-sidetip.tooltipster-light .tooltipster-content {\n    padding: 8px 12px !important;\n    font-size: 12px !important;\n    line-height: 1.4 !important;\n    color: #1e293b !important;\n}\n.tooltipster-sidetip.tooltipster-light .tooltipster-arrow-background {\n    border-top-color: #ffffff !important;\n}\n.tooltipster-sidetip.tooltipster-light .tooltipster-arrow-border {\n    border-top-color: #e2e8f0 !important;\n}\n\n@media (max-width: 1200px) {\n    .awesome-reports-uptime-grid { grid-template-columns: repeat(2, 1fr); }\n    .ar-uptime-response { grid-column: span 2; }\n}\n\n@media (max-width: 600px) {\n    .awesome-reports-uptime-grid { grid-template-columns: 1fr; }\n    .ar-uptime-response { grid-column: span 1; }\n}\n\n@media (max-width: 1200px) {\n    .awesome-reports-grid {\n        grid-template-columns: repeat(2, 1fr);\n    }\n}\n\n@media (max-width: 600px) {\n    .awesome-reports-toolbar-primary {\n        padding: 8px 15px;\n        margin: -10px -20px 0 -20px;\n    }\n\n    .awesome-reports-toolbar-secondary {\n        padding: 10px 15px;\n        margin: 0 -20px 15px -20px;\n    }\n\n    .awesome-reports-logo-text {\n        font-size: 16px;\n    }\n\n    .awesome-reports-logo-icon {\n        width: 36px;\n        height: 36px;\n    }\n\n    .awesome-reports-logo-icon svg {\n        width: 20px;\n        height: 20px;\n    }\n\n    .awesome-reports-time-filter {\n        min-width: 140px;\n        font-size: 13px;\n        padding: 6px 10px;\n        height: 30px;\n    }\n\n    .awesome-reports-grid {\n        grid-template-columns: 1fr;\n    }\n\n    .awesome-reports-update-card .count {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 5px;\n    }\n\n    .awesome-reports-update-card .update-item {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 4px;\n        padding: 10px 12px;\n    }\n\n    .awesome-reports-update-card .update-date {\n        min-width: auto;\n        order: 2;\n    }\n\n    .awesome-reports-update-card .update-name {\n        order: 1;\n        font-size: 13px;\n    }\n\n    .awesome-reports-update-card .update-version {\n        order: 3;\n        align-self: flex-end;\n    }\n}\n", "/* Toolbar Styles */\n.awesome-reports-toolbar-primary {\n  background: #fff;\n  padding: 10px 0;\n  margin: 0 0 0 -20px;\n  border-bottom: 1px solid #ddd;\n}\n\n.awesome-reports-logo {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 0 20px;\n  text-decoration: none;\n}\n\n.awesome-reports-logo:focus,\n.awesome-reports-logo:hover,\n.awesome-reports-logo:active,\n.awesome-reports-logo:visited {\n  text-decoration: none;\n  outline: none;\n  box-shadow: none;\n}\n\n.awesome-reports-logo-icon {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.awesome-reports-logo-icon svg {\n  width: 40px;\n  height: 40px;\n}\n\n.awesome-reports-logo-text {\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.awesome-reports-toolbar-secondary {\n  background: #fff;\n  border-bottom: 1px solid #ddd;\n  padding: 12px 0;\n  margin: 0 0 20px -20px;\n}\n\n.awesome-reports-filters {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 0 20px;\n}\n\n.awesome-reports-time-filter {\n  background: #fff;\n  border: 1px solid #c3c4c7 !important;\n  border-radius: 4px;\n  padding: 8px 12px;\n  font-size: 14px;\n  color: #1d2327;\n  min-width: 160px;\n  height: 32px;\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.awesome-reports-time-filter:focus {\n  border-color: #2271b1;\n  box-shadow: 0 0 0 1px #2271b1;\n  outline: none;\n}\n\n.awesome-reports-time-filter:hover {\n  border-color: #8c8f94;\n}\n\n.awesome-reports-table {\n  width: 100% !important;\n  max-width: none !important;\n}\n\n.awesome-reports-table th,\n.awesome-reports-table td {\n  padding: 15px 10px !important;\n}\n\n.awesome-reports-card {\n  margin-bottom: 20px;\n  max-width: 100%;\n}\n\n.awesome-reports-card .card {\n  max-width: none !important;\n}\n\n.awesome-reports-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n  margin-bottom: 30px;\n}\n\n.awesome-reports-update-card {\n  background: #fff;\n  border: 1px solid #ddd;\n  padding: 15px 15px 5px 15px;\n  border-radius: 6px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n\n.awesome-reports-update-card h3 {\n  margin: 0 0 15px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #23282d;\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 10px 15px;\n  margin: -15px -15px 15px -15px;\n  border-bottom: 1px solid #e1e1e1;\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n\n.awesome-reports-update-card .dashicons {\n  width: 20px;\n  height: 20px;\n  font-size: 20px;\n  color: #98a2b3;\n}\n\n.awesome-reports-update-card .count {\n  font-size: 48px;\n  font-weight: bold;\n  color: #1d2327;\n  margin: 10px 0 5px 0;\n  line-height: 1;\n  display: flex;\n  align-items: baseline;\n  gap: 10px;\n}\n\n.awesome-reports-update-card .updates-list {\n  font-size: 13px;\n  color: #646970;\n  margin: 15px -15px 0 -15px;\n  opacity: 0.8;\n}\n\n.update-more,\n.update-none {\n  color: #646970;\n  font-style: italic;\n  padding: 10px 15px;\n}\n\n/* Table-style update items like incidents */\n.awesome-reports-update-card .update-item {\n  margin-bottom: 0;\n  padding: 10px 12px;\n  border-bottom: 1px solid #f3f4f6;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 12px;\n  background: #fff;\n  font-size: 13px;\n  color: #111827;\n  line-height: 1.4;\n}\n\n.awesome-reports-update-card .update-item:nth-child(odd) {\n  background: #fafafa;\n}\n\n.awesome-reports-update-card .update-item:first-child {\n  border-top: 1px solid #e5e7eb;\n}\n\n.awesome-reports-update-card .update-date {\n  font-weight: 500;\n  color: #6b7280;\n  flex-shrink: 0;\n  min-width: 55px;\n  font-size: 13px;\n}\n\n.awesome-reports-update-card .update-name {\n  flex: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  font-weight: 500;\n  color: #111827;\n  font-size: 13px;\n}\n\n.awesome-reports-update-card .update-version {\n  color: #6b7280;\n  flex-shrink: 0;\n  font-weight: 500;\n  font-size: 13px;\n}\n\n/* Uptime Grid + Cards */\n.awesome-reports-uptime-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n  margin-bottom: 30px;\n}\n\n.ar-uptime-stack {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  height: 100%;\n}\n\n.ar-uptime-stack .awesome-reports-update-card {\n  flex: 1;\n}\n\n.ar-uptime-response {\n  grid-column: span 2;\n}\n\n.ar-uptime-incidents {\n  grid-column: span 1;\n}\n\n.ar-uptime-overview .ar-overview-top {\n  margin-bottom: 4px;\n}\n\n.ar-overview-divider {\n  height: 1px;\n  background: #eef0f2;\n  margin: 10px 0 12px;\n}\n\n.ar-uptime-overview .ar-overview-bottom {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.ar-uptime-card .ar-subtle {\n  color: #646970;\n  margin-top: 8px;\n}\n\n.ar-status-row {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin: 8px 0 12px;\n}\n\n.ar-status-dot {\n  width: 18px;\n  height: 18px;\n  border-radius: 50%;\n  background: #22c55e;\n  box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.2);\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.2);\n  }\n  50% {\n    box-shadow: 0 0 0 6px rgba(34, 197, 94, 0.3);\n  }\n  100% {\n    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.2);\n  }\n}\n.ar-status-text {\n  font-size: 24px;\n  font-weight: 800;\n}\n\n.ar-badge {\n  display: inline-block;\n  padding: 4px 10px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 600;\n}\n\n.ar-badge-up {\n  background: #e8f5e8;\n  color: #0b7a0b;\n  border: 1px solid #c6e1c6;\n}\n\n.ar-big-number {\n  font-size: 40px;\n  font-weight: 800;\n  line-height: 1;\n  color: #1d2327;\n  margin-bottom: 10px;\n}\n\n.ar-uptime-response {\n  grid-column: span 2;\n  overflow: hidden;\n}\n\n.ar-chart-wrap {\n  height: 240px;\n  overflow: hidden;\n  position: relative;\n}\n\n.ar-chart-wrap canvas {\n  width: 100% !important;\n  height: 100% !important;\n  max-width: 100%;\n}\n\n/* Pills */\n.ar-pills-panel {\n  background: transparent;\n  border: none;\n  padding: 0;\n  margin-bottom: 15px;\n}\n\n.ar-pills-head {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 10px;\n}\n\n.ar-pills-title {\n  font-weight: 600;\n  font-size: 14px;\n  color: #1e293b;\n}\n\n.ar-pills-percent {\n  font-weight: 700;\n  font-size: 14px;\n  color: #1e293b;\n}\n\n.ar-pills {\n  display: grid;\n  grid-template-columns: repeat(30, 1fr);\n  gap: 4px;\n  padding: 0;\n  margin-bottom: 8px;\n}\n\n.ar-pill {\n  width: 100%;\n  height: 32px;\n  border-radius: 999px;\n  background: #22c55e;\n  border: none;\n  transition: opacity 120ms ease;\n}\n\n.ar-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.ar-table thead th {\n  text-align: left;\n  font-weight: 600;\n  font-size: 12px;\n  color: #6b7280;\n  padding: 8px 12px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.ar-table tbody td {\n  padding: 10px 12px;\n  border-bottom: 1px solid #f3f4f6;\n  font-size: 13px;\n  color: #111827;\n}\n\n.ar-table tbody tr:nth-child(odd) td {\n  background: #fafafa;\n}\n\n.ar-pill-status {\n  display: inline-block;\n  padding: 2px 8px;\n  border-radius: 10px;\n  font-size: 12px;\n}\n\n.ar-pill-status.resolved {\n  background: #e8f5e8;\n  color: #0b7a0b;\n  border: 1px solid #c6e1c6;\n}\n\n.ar-pill:hover {\n  opacity: 0.7;\n}\n\n.ar-pill.up {\n  background: #22c55e;\n}\n\n.ar-pill.down {\n  background: #ef4444;\n}\n\n.ar-pills-foot {\n  color: #64748b;\n}\n\n/* light tooltipster theme */\n.tooltipster-sidetip.tooltipster-light .tooltipster-box {\n  background: #ffffff !important;\n  color: #1e293b !important;\n  border: 1px solid #e2e8f0 !important;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;\n  border-radius: 6px !important;\n}\n\n.tooltipster-sidetip.tooltipster-light .tooltipster-content {\n  padding: 8px 12px !important;\n  font-size: 12px !important;\n  line-height: 1.4 !important;\n  color: #1e293b !important;\n}\n\n.tooltipster-sidetip.tooltipster-light .tooltipster-arrow-background {\n  border-top-color: #ffffff !important;\n}\n\n.tooltipster-sidetip.tooltipster-light .tooltipster-arrow-border {\n  border-top-color: #e2e8f0 !important;\n}\n\n@media (max-width: 1200px) {\n  .awesome-reports-uptime-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  .ar-uptime-response {\n    grid-column: span 2;\n  }\n}\n@media (max-width: 600px) {\n  .awesome-reports-uptime-grid {\n    grid-template-columns: 1fr;\n  }\n  .ar-uptime-response {\n    grid-column: span 1;\n  }\n}\n@media (max-width: 1200px) {\n  .awesome-reports-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n@media (max-width: 600px) {\n  .awesome-reports-toolbar-primary {\n    padding: 8px 15px;\n    margin: -10px -20px 0 -20px;\n  }\n  .awesome-reports-toolbar-secondary {\n    padding: 10px 15px;\n    margin: 0 -20px 15px -20px;\n  }\n  .awesome-reports-logo-text {\n    font-size: 16px;\n  }\n  .awesome-reports-logo-icon {\n    width: 36px;\n    height: 36px;\n  }\n  .awesome-reports-logo-icon svg {\n    width: 20px;\n    height: 20px;\n  }\n  .awesome-reports-time-filter {\n    min-width: 140px;\n    font-size: 13px;\n    padding: 6px 10px;\n    height: 30px;\n  }\n  .awesome-reports-grid {\n    grid-template-columns: 1fr;\n  }\n  .awesome-reports-update-card .count {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n  .awesome-reports-update-card .update-item {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 4px;\n    padding: 10px 12px;\n  }\n  .awesome-reports-update-card .update-date {\n    min-width: auto;\n    order: 2;\n  }\n  .awesome-reports-update-card .update-name {\n    order: 1;\n    font-size: 13px;\n  }\n  .awesome-reports-update-card .update-version {\n    order: 3;\n    align-self: flex-end;\n  }\n}"]}