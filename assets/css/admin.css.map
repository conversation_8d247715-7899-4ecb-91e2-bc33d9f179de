{"version": 3, "sources": ["admin.scss", "admin.css"], "names": [], "mappings": "AAGA,mBAAA;AACA;EACI,gBAAA;EACA,eAAA;EACA,mBAAA;EACA,6BAAA;ACFJ;ADII;EANJ;IAOQ,iBAAA;IACA,2BAAA;ECDN;AACF;;ADIA;EACI,aAAA;EACA,mBAAA;EACA,SAAA;EACA,eAAA;EACA,qBAAA;ACDJ;;ADIA;;;;EAII,qBAAA;EACA,aAAA;EACA,gBAAA;ACDJ;;ADIA;EACI,WAAA;EACA,YAAA;EACA,aAAA;EACA,mBAAA;EACA,uBAAA;EACA,cAAA;ACDJ;ADGI;EARJ;IASQ,WAAA;IACA,YAAA;ECAN;AACF;;ADGA;EACI,WAAA;EACA,YAAA;ACAJ;ADEI;EAJJ;IAKQ,WAAA;IACA,YAAA;ECCN;AACF;;ADEA;EACI,eAAA;EACA,gBAAA;ACCJ;ADCI;EAJJ;IAKQ,eAAA;ECEN;AACF;;ADCA;EACI,gBAAA;EACA,6BAAA;EACA,eAAA;EACA,sBAAA;ACEJ;ADAI;EANJ;IAOQ,kBAAA;IACA,0BAAA;ECGN;AACF;;ADAA;EACI,aAAA;EACA,mBAAA;EACA,SAAA;EACA,eAAA;ACGJ;;ADAA;EACI,gBAAA;EACA,oCAAA;EACA,kBAAA;EACA,iBAAA;EACA,eAAA;EACA,cAAA;EACA,gBAAA;EACA,YAAA;EACA,yCAAA;EACA,wEAAA;ACGJ;ADDI;EAZJ;IAaQ,gBAAA;IACA,eAAA;IACA,iBAAA;IACA,YAAA;ECIN;AACF;;ADDA;EACI,qBAAA;EACA,6BAAA;EACA,aAAA;ACIJ;;ADDA;EACI,qBAAA;ACIJ;;ADDA;EACI,sBAAA;EACA,0BAAA;ACIJ;;ADDA;;EAEI,6BAAA;ACIJ;;ADDA;EACI,mBAAA;EACA,eAAA;ACIJ;;ADDA;EACI,0BAAA;ACIJ;;ADDA;EACI,aAAA;EACA,qCAAA;EACA,SAAA;EACA,mBAAA;ACIJ;ADFI;EANJ;IAOQ,qCAAA;ECKN;AACF;ADHI;EAVJ;IAWQ,0BAAA;ECMN;AACF;;ADHA;EACI,gBAAA;EACA,sBAAA;EACA,2BAAA;EACA,kBAAA;EACA,6CAAA;ACMJ;ADJI;EACI;IACI,sBAAA;IACA,uBAAA;IACA,QAAA;ECMV;AACF;;ADFA;EACI,kBAAA;EACA,eAAA;EACA,gBAAA;EACA,cAAA;EACA,aAAA;EACA,mBAAA;EACA,SAAA;EACA,kBAAA;EACA,8BAAA;EACA,gCAAA;EACA,2BAAA;EACA,4BAAA;ACKJ;;ADFA;EACI,WAAA;EACA,YAAA;EACA,eAAA;EACA,cAAA;ACKJ;;ADFA;EACI,eAAA;EACA,iBAAA;EACA,cAAA;EACA,oBAAA;EACA,cAAA;EACA,aAAA;EACA,qBAAA;EACA,SAAA;ACKJ;;ADDA;;EAEI,cAAA;EACA,kBAAA;EACA,kBAAA;ACIJ;;ADAA,wBAAA;AACA;EACI,aAAA;EACA,qCAAA;EACA,SAAA;EACA,mBAAA;ACGJ;;ADDA;EACI,aAAA;EACA,sBAAA;EACA,SAAA;EACA,YAAA;ACIJ;;ADFA;EACI,OAAA;ACKJ;;ADHA;EAAsB,mBAAA;ACOtB;;ADNA;EAAuB,mBAAA;ACUvB;;ADRA;EAAuC,kBAAA;ACYvC;;ADXA;EAAuB,WAAA;EAAa,mBAAA;EAAqB,mBAAA;ACiBzD;;ADhBA;EAA0C,aAAA;EAAe,sBAAA;EAAwB,SAAA;ACsBjF;;ADpBA;EACI,cAAA;EACA,eAAA;ACuBJ;;ADpBA;EACI,aAAA;EACA,mBAAA;EACA,SAAA;EACA,kBAAA;ACuBJ;;ADrBA;EACI,WAAA;EACA,YAAA;EACA,kBAAA;EACA,mBAAA;EACA,4CAAA;EACA,4BAAA;ACwBJ;;ADtBA;EACI;IAAK,4CAAA;EC0BP;EDzBE;IAAM,4CAAA;EC4BR;ED3BE;IAAO,4CAAA;EC8BT;AACF;AD7BA;EACI,eAAA;EACA,gBAAA;AC+BJ;;AD5BA;EACI,qBAAA;EACA,iBAAA;EACA,mBAAA;EACA,eAAA;EACA,gBAAA;AC+BJ;;AD7BA;EACI,mBAAA;EACA,cAAA;EACA,yBAAA;ACgCJ;;AD7BA;EACI,eAAA;EACA,gBAAA;EACA,cAAA;EACA,cAAA;EACA,mBAAA;ACgCJ;;AD7BA;EACI,mBAAA;EACA,gBAAA;ACgCJ;;AD9BA;EACI,aAAA;EACA,gBAAA;EACA,kBAAA;ACiCJ;;AD/BA;EACI,sBAAA;EACA,uBAAA;EACA,eAAA;ACkCJ;;AD/BA,UAAA;AACA;EACI,uBAAA;EACA,YAAA;EACA,UAAA;EACA,mBAAA;ACkCJ;;ADhCA;EACI,aAAA;EACA,mBAAA;EACA,8BAAA;EACA,mBAAA;ACmCJ;;ADjCA;EAAkB,gBAAA;EAAkB,eAAA;EAAiB,cAAA;ACuCrD;;ADtCA;EAAoB,gBAAA;EAAkB,eAAA;EAAiB,cAAA;AC4CvD;;AD1CA;EACI,aAAA;EACA,sCAAA;EACA,QAAA;EACA,UAAA;EACA,kBAAA;AC6CJ;;AD3CA;EACI,WAAA;EACA,YAAA;EACA,oBAAA;EACA,mBAAA;EACA,YAAA;EACA,8BAAA;AC8CJ;;AD5CA;EAAY,WAAA;EAAa,yBAAA;ACiDzB;;ADhDA;EAAqB,gBAAA;EAAkB,gBAAA;EAAkB,eAAA;EAAiB,cAAA;EAAgB,iBAAA;EAAmB,gCAAA;ACyD7G;;ADxDA;EAAqB,kBAAA;EAAoB,gCAAA;EAAkC,eAAA;EAAiB,cAAA;AC+D5F;;AD9DA;EAAuC,mBAAA;ACkEvC;;ADjEA;EAAkB,qBAAA;EAAuB,gBAAA;EAAkB,mBAAA;EAAqB,eAAA;ACwEhF;;ADvEA;EAA2B,mBAAA;EAAqB,cAAA;EAAgB,yBAAA;AC6EhE;;AD3EA;EACI,YAAA;AC8EJ;;AD5EA;EAAc,mBAAA;ACgFd;;AD/EA;EAAgB,mBAAA;ACmFhB;;ADjFA;EACI,cAAA;ACoFJ;;ADjFA,4BAAA;AACA;EACI,8BAAA;EACA,yBAAA;EACA,oCAAA;EACA,qDAAA;EACA,6BAAA;ACoFJ;;ADlFA;EACI,4BAAA;EACA,0BAAA;EACA,2BAAA;EACA,yBAAA;ACqFJ;;ADnFA;EACI,oCAAA;ACsFJ;;ADpFA;EACI,oCAAA;ACuFJ;;ADpFA;EACI;IAAkB,qCAAA;ECwFpB;EDvFE;IAAsB,mBAAA;EC0FxB;AACF;ADxFA;EACI;IAAkB,0BAAA;EC2FpB;ED1FE;IAAsB,mBAAA;EC6FxB;AACF", "file": "admin.css", "sourcesContent": ["// Source SCSS for Awesome Reports admin styles\n// This file is compiled to assets/css/admin.css via Gulp\n\n/* Toolbar Styles */\n.ar-toolbar-primary {\n    background: #fff;\n    padding: 10px 0;\n    margin: 0 0 0 -20px;\n    border-bottom: 1px solid #ddd;\n\n    @media (max-width: 600px) {\n        padding: 8px 15px;\n        margin: -10px -20px 0 -20px;\n    }\n}\n\n.ar-logo {\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    padding: 0 20px;\n    text-decoration: none;\n}\n\n.ar-logo:focus,\n.ar-logo:hover,\n.ar-logo:active,\n.ar-logo:visited {\n    text-decoration: none;\n    outline: none;\n    box-shadow: none;\n}\n\n.ar-logo-icon {\n    width: 40px;\n    height: 40px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-shrink: 0;\n\n    @media (max-width: 600px) {\n        width: 36px;\n        height: 36px;\n    }\n}\n\n.ar-logo-icon svg {\n    width: 40px;\n    height: 40px;\n\n    @media (max-width: 600px) {\n        width: 20px;\n        height: 20px;\n    }\n}\n\n.ar-logo-text {\n    font-size: 18px;\n    font-weight: 600;\n\n    @media (max-width: 600px) {\n        font-size: 16px;\n    }\n}\n\n.ar-toolbar-secondary {\n    background: #fff;\n    border-bottom: 1px solid #ddd;\n    padding: 12px 0;\n    margin: 0 0 20px -20px;\n\n    @media (max-width: 600px) {\n        padding: 10px 15px;\n        margin: 0 -20px 15px -20px;\n    }\n}\n\n.ar-filters {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n    padding: 0 20px;\n}\n\n.ar-time-filter {\n    background: #fff;\n    border: 1px solid #c3c4c7!important;\n    border-radius: 4px;\n    padding: 8px 12px;\n    font-size: 14px;\n    color: #1d2327;\n    min-width: 160px;\n    height: 32px;\n    box-shadow: 0 1px 1px rgba(0,0,0,.04);\n    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n\n    @media (max-width: 600px) {\n        min-width: 140px;\n        font-size: 13px;\n        padding: 6px 10px;\n        height: 30px;\n    }\n}\n\n.ar-time-filter:focus {\n    border-color: #2271b1;\n    box-shadow: 0 0 0 1px #2271b1;\n    outline: none;\n}\n\n.ar-time-filter:hover {\n    border-color: #8c8f94;\n}\n\n.ar-table {\n    width: 100% !important;\n    max-width: none !important;\n}\n\n.ar-table th,\n.ar-table td {\n    padding: 12px 10px !important;\n}\n\n.ar-card {\n    margin-bottom: 20px;\n    max-width: 100%;\n}\n\n.ar-card .card {\n    max-width: none !important;\n}\n\n.ar-grid {\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    gap: 12px;\n    margin-bottom: 30px;\n\n    @media (max-width: 1200px) {\n        grid-template-columns: repeat(2, 1fr);\n    }\n\n    @media (max-width: 600px) {\n        grid-template-columns: 1fr;\n    }\n}\n\n.ar-update-card {\n    background: #fff;\n    border: 1px solid #ddd;\n    padding: 15px 15px 5px 15px;\n    border-radius: 6px;\n    box-shadow: 0px 1px 2px rgba(16, 24, 40, .1);\n\n    @media (max-width: 600px) {\n        .count {\n            flex-direction: column;\n            align-items: flex-start;\n            gap: 5px;\n        }\n    }\n}\n\n.ar-update-card h3 {\n    margin: 0 0 15px 0;\n    font-size: 14px;\n    font-weight: 600;\n    color: #23282d;\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    padding: 10px 15px;\n    margin: -15px -15px 15px -15px;\n    border-bottom: 1px solid #e1e1e1;\n    border-top-left-radius: 8px;\n    border-top-right-radius: 8px;\n}\n\n.ar-update-card .dashicons {\n    width: 20px;\n    height: 20px;\n    font-size: 20px;\n    color: #98a2b3;\n}\n\n.ar-update-card .count {\n    font-size: 48px;\n    font-weight: bold;\n    color: #1d2327;\n    margin: 10px 0 5px 0;\n    line-height: 1;\n    display: flex;\n    align-items: baseline;\n    gap: 10px;\n}\n\n\n.update-more,\n.update-none {\n    color: #646970;\n    font-style: italic;\n    padding: 10px 15px;\n}\n\n\n/* Uptime Grid + Cards */\n.ar-uptime-grid {\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    gap: 12px;\n    margin-bottom: 30px;\n}\n.ar-uptime-stack {\n    display: flex;\n    flex-direction: column;\n    gap: 12px;\n    height: 100%;\n}\n.ar-uptime-stack .ar-update-card {\n    flex: 1;\n}\n.ar-uptime-response { grid-column: span 2; }\n.ar-uptime-incidents { grid-column: span 1; }\n\n.ar-uptime-overview .ar-overview-top { margin-bottom: 4px; }\n.ar-overview-divider { height: 1px; background: #eef0f2; margin: 10px 0 12px; }\n.ar-uptime-overview .ar-overview-bottom { display: flex; flex-direction: column; gap: 10px; }\n\n.ar-uptime-card .ar-subtle {\n    color: #646970;\n    margin-top: 8px;\n}\n\n.ar-status-row {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n    margin: 8px 0 12px;\n}\n.ar-status-dot {\n    width: 18px;\n    height: 18px;\n    border-radius: 50%;\n    background: #22c55e;\n    box-shadow: 0 0 0 4px rgba(34,197,94,0.2);\n    animation: pulse 2s infinite;\n}\n@keyframes pulse {\n    0% { box-shadow: 0 0 0 4px rgba(34,197,94,0.2); }\n    50% { box-shadow: 0 0 0 6px rgba(34,197,94,0.3); }\n    100% { box-shadow: 0 0 0 4px rgba(34,197,94,0.2); }\n}\n.ar-status-text {\n    font-size: 24px;\n    font-weight: 800;\n}\n\n.ar-badge {\n    display: inline-block;\n    padding: 4px 10px;\n    border-radius: 12px;\n    font-size: 12px;\n    font-weight: 600;\n}\n.ar-badge-up {\n    background: #e8f5e8;\n    color: #0b7a0b;\n    border: 1px solid #c6e1c6;\n}\n\n.ar-big-number {\n    font-size: 40px;\n    font-weight: 800;\n    line-height: 1;\n    color: #1d2327;\n    margin-bottom: 10px;\n}\n\n.ar-uptime-response {\n    grid-column: span 2;\n    overflow: hidden;\n}\n.ar-chart-wrap {\n    height: 240px;\n    overflow: hidden;\n    position: relative;\n}\n.ar-chart-wrap canvas {\n    width: 100% !important;\n    height: 100% !important;\n    max-width: 100%;\n}\n\n/* Pills */\n.ar-pills-panel {\n    background: transparent;\n    border: none;\n    padding: 0;\n    margin-bottom: 15px;\n}\n.ar-pills-head {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 10px;\n}\n.ar-pills-title { font-weight: 600; font-size: 14px; color: #1e293b; }\n.ar-pills-percent { font-weight: 700; font-size: 14px; color: #1e293b; }\n\n.ar-pills {\n    display: grid;\n    grid-template-columns: repeat(30, 1fr);\n    gap: 4px;\n    padding: 0;\n    margin-bottom: 8px;\n}\n.ar-pill {\n    width: 100%;\n    height: 32px;\n    border-radius: 999px;\n    background: #22c55e;\n    border: none;\n    transition: opacity 120ms ease;\n}\n.ar-table { width: 100%; border-collapse: collapse; }\n.ar-table thead th { text-align: left; font-weight: 600; font-size: 12px; color: #6b7280; padding: 8px 12px; border-bottom: 1px solid #e5e7eb; }\n.ar-table tbody td { padding: 10px 12px; border-bottom: 1px solid #f3f4f6; font-size: 13px; color: #111827; }\n.ar-table tbody tr:nth-child(odd) td { background: #fafafa; }\n.ar-pill-status { display: inline-block; padding: 2px 8px; border-radius: 10px; font-size: 12px; }\n.ar-pill-status.resolved { background: #e8f5e8; color: #0b7a0b; border: 1px solid #c6e1c6; }\n\n.ar-pill:hover {\n    opacity: 0.7;\n}\n.ar-pill.up { background: #22c55e; }\n.ar-pill.down { background: #ef4444; }\n\n.ar-pills-foot {\n    color: #64748b;\n}\n\n/* light tooltipster theme */\n.tooltipster-sidetip.tooltipster-light .tooltipster-box {\n    background: #ffffff !important;\n    color: #1e293b !important;\n    border: 1px solid #e2e8f0 !important;\n    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;\n    border-radius: 6px !important;\n}\n.tooltipster-sidetip.tooltipster-light .tooltipster-content {\n    padding: 8px 12px !important;\n    font-size: 12px !important;\n    line-height: 1.4 !important;\n    color: #1e293b !important;\n}\n.tooltipster-sidetip.tooltipster-light .tooltipster-arrow-background {\n    border-top-color: #ffffff !important;\n}\n.tooltipster-sidetip.tooltipster-light .tooltipster-arrow-border {\n    border-top-color: #e2e8f0 !important;\n}\n\n@media (max-width: 1200px) {\n    .ar-uptime-grid { grid-template-columns: repeat(2, 1fr); }\n    .ar-uptime-response { grid-column: span 2; }\n}\n\n@media (max-width: 600px) {\n    .ar-uptime-grid { grid-template-columns: 1fr; }\n    .ar-uptime-response { grid-column: span 1; }\n}\n\n\n", "/* Toolbar Styles */\n.ar-toolbar-primary {\n  background: #fff;\n  padding: 10px 0;\n  margin: 0 0 0 -20px;\n  border-bottom: 1px solid #ddd;\n}\n@media (max-width: 600px) {\n  .ar-toolbar-primary {\n    padding: 8px 15px;\n    margin: -10px -20px 0 -20px;\n  }\n}\n\n.ar-logo {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 0 20px;\n  text-decoration: none;\n}\n\n.ar-logo:focus,\n.ar-logo:hover,\n.ar-logo:active,\n.ar-logo:visited {\n  text-decoration: none;\n  outline: none;\n  box-shadow: none;\n}\n\n.ar-logo-icon {\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n@media (max-width: 600px) {\n  .ar-logo-icon {\n    width: 36px;\n    height: 36px;\n  }\n}\n\n.ar-logo-icon svg {\n  width: 40px;\n  height: 40px;\n}\n@media (max-width: 600px) {\n  .ar-logo-icon svg {\n    width: 20px;\n    height: 20px;\n  }\n}\n\n.ar-logo-text {\n  font-size: 18px;\n  font-weight: 600;\n}\n@media (max-width: 600px) {\n  .ar-logo-text {\n    font-size: 16px;\n  }\n}\n\n.ar-toolbar-secondary {\n  background: #fff;\n  border-bottom: 1px solid #ddd;\n  padding: 12px 0;\n  margin: 0 0 20px -20px;\n}\n@media (max-width: 600px) {\n  .ar-toolbar-secondary {\n    padding: 10px 15px;\n    margin: 0 -20px 15px -20px;\n  }\n}\n\n.ar-filters {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 0 20px;\n}\n\n.ar-time-filter {\n  background: #fff;\n  border: 1px solid #c3c4c7 !important;\n  border-radius: 4px;\n  padding: 8px 12px;\n  font-size: 14px;\n  color: #1d2327;\n  min-width: 160px;\n  height: 32px;\n  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n@media (max-width: 600px) {\n  .ar-time-filter {\n    min-width: 140px;\n    font-size: 13px;\n    padding: 6px 10px;\n    height: 30px;\n  }\n}\n\n.ar-time-filter:focus {\n  border-color: #2271b1;\n  box-shadow: 0 0 0 1px #2271b1;\n  outline: none;\n}\n\n.ar-time-filter:hover {\n  border-color: #8c8f94;\n}\n\n.ar-table {\n  width: 100% !important;\n  max-width: none !important;\n}\n\n.ar-table th,\n.ar-table td {\n  padding: 12px 10px !important;\n}\n\n.ar-card {\n  margin-bottom: 20px;\n  max-width: 100%;\n}\n\n.ar-card .card {\n  max-width: none !important;\n}\n\n.ar-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n  margin-bottom: 30px;\n}\n@media (max-width: 1200px) {\n  .ar-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n@media (max-width: 600px) {\n  .ar-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n.ar-update-card {\n  background: #fff;\n  border: 1px solid #ddd;\n  padding: 15px 15px 5px 15px;\n  border-radius: 6px;\n  box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.1);\n}\n@media (max-width: 600px) {\n  .ar-update-card .count {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 5px;\n  }\n}\n\n.ar-update-card h3 {\n  margin: 0 0 15px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #23282d;\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 10px 15px;\n  margin: -15px -15px 15px -15px;\n  border-bottom: 1px solid #e1e1e1;\n  border-top-left-radius: 8px;\n  border-top-right-radius: 8px;\n}\n\n.ar-update-card .dashicons {\n  width: 20px;\n  height: 20px;\n  font-size: 20px;\n  color: #98a2b3;\n}\n\n.ar-update-card .count {\n  font-size: 48px;\n  font-weight: bold;\n  color: #1d2327;\n  margin: 10px 0 5px 0;\n  line-height: 1;\n  display: flex;\n  align-items: baseline;\n  gap: 10px;\n}\n\n.update-more,\n.update-none {\n  color: #646970;\n  font-style: italic;\n  padding: 10px 15px;\n}\n\n/* Uptime Grid + Cards */\n.ar-uptime-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 12px;\n  margin-bottom: 30px;\n}\n\n.ar-uptime-stack {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  height: 100%;\n}\n\n.ar-uptime-stack .ar-update-card {\n  flex: 1;\n}\n\n.ar-uptime-response {\n  grid-column: span 2;\n}\n\n.ar-uptime-incidents {\n  grid-column: span 1;\n}\n\n.ar-uptime-overview .ar-overview-top {\n  margin-bottom: 4px;\n}\n\n.ar-overview-divider {\n  height: 1px;\n  background: #eef0f2;\n  margin: 10px 0 12px;\n}\n\n.ar-uptime-overview .ar-overview-bottom {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.ar-uptime-card .ar-subtle {\n  color: #646970;\n  margin-top: 8px;\n}\n\n.ar-status-row {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin: 8px 0 12px;\n}\n\n.ar-status-dot {\n  width: 18px;\n  height: 18px;\n  border-radius: 50%;\n  background: #22c55e;\n  box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.2);\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0% {\n    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.2);\n  }\n  50% {\n    box-shadow: 0 0 0 6px rgba(34, 197, 94, 0.3);\n  }\n  100% {\n    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.2);\n  }\n}\n.ar-status-text {\n  font-size: 24px;\n  font-weight: 800;\n}\n\n.ar-badge {\n  display: inline-block;\n  padding: 4px 10px;\n  border-radius: 12px;\n  font-size: 12px;\n  font-weight: 600;\n}\n\n.ar-badge-up {\n  background: #e8f5e8;\n  color: #0b7a0b;\n  border: 1px solid #c6e1c6;\n}\n\n.ar-big-number {\n  font-size: 40px;\n  font-weight: 800;\n  line-height: 1;\n  color: #1d2327;\n  margin-bottom: 10px;\n}\n\n.ar-uptime-response {\n  grid-column: span 2;\n  overflow: hidden;\n}\n\n.ar-chart-wrap {\n  height: 240px;\n  overflow: hidden;\n  position: relative;\n}\n\n.ar-chart-wrap canvas {\n  width: 100% !important;\n  height: 100% !important;\n  max-width: 100%;\n}\n\n/* Pills */\n.ar-pills-panel {\n  background: transparent;\n  border: none;\n  padding: 0;\n  margin-bottom: 15px;\n}\n\n.ar-pills-head {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 10px;\n}\n\n.ar-pills-title {\n  font-weight: 600;\n  font-size: 14px;\n  color: #1e293b;\n}\n\n.ar-pills-percent {\n  font-weight: 700;\n  font-size: 14px;\n  color: #1e293b;\n}\n\n.ar-pills {\n  display: grid;\n  grid-template-columns: repeat(30, 1fr);\n  gap: 4px;\n  padding: 0;\n  margin-bottom: 8px;\n}\n\n.ar-pill {\n  width: 100%;\n  height: 32px;\n  border-radius: 999px;\n  background: #22c55e;\n  border: none;\n  transition: opacity 120ms ease;\n}\n\n.ar-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.ar-table thead th {\n  text-align: left;\n  font-weight: 600;\n  font-size: 12px;\n  color: #6b7280;\n  padding: 8px 12px;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.ar-table tbody td {\n  padding: 10px 12px;\n  border-bottom: 1px solid #f3f4f6;\n  font-size: 13px;\n  color: #111827;\n}\n\n.ar-table tbody tr:nth-child(odd) td {\n  background: #fafafa;\n}\n\n.ar-pill-status {\n  display: inline-block;\n  padding: 2px 8px;\n  border-radius: 10px;\n  font-size: 12px;\n}\n\n.ar-pill-status.resolved {\n  background: #e8f5e8;\n  color: #0b7a0b;\n  border: 1px solid #c6e1c6;\n}\n\n.ar-pill:hover {\n  opacity: 0.7;\n}\n\n.ar-pill.up {\n  background: #22c55e;\n}\n\n.ar-pill.down {\n  background: #ef4444;\n}\n\n.ar-pills-foot {\n  color: #64748b;\n}\n\n/* light tooltipster theme */\n.tooltipster-sidetip.tooltipster-light .tooltipster-box {\n  background: #ffffff !important;\n  color: #1e293b !important;\n  border: 1px solid #e2e8f0 !important;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;\n  border-radius: 6px !important;\n}\n\n.tooltipster-sidetip.tooltipster-light .tooltipster-content {\n  padding: 8px 12px !important;\n  font-size: 12px !important;\n  line-height: 1.4 !important;\n  color: #1e293b !important;\n}\n\n.tooltipster-sidetip.tooltipster-light .tooltipster-arrow-background {\n  border-top-color: #ffffff !important;\n}\n\n.tooltipster-sidetip.tooltipster-light .tooltipster-arrow-border {\n  border-top-color: #e2e8f0 !important;\n}\n\n@media (max-width: 1200px) {\n  .ar-uptime-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n  .ar-uptime-response {\n    grid-column: span 2;\n  }\n}\n@media (max-width: 600px) {\n  .ar-uptime-grid {\n    grid-template-columns: 1fr;\n  }\n  .ar-uptime-response {\n    grid-column: span 1;\n  }\n}"]}